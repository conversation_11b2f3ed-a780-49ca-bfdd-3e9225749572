/* eslint-disable @typescript-eslint/no-explicit-any */
import * as THREE from 'three';
import { ModelService } from './modelService';
import { AIService, AIResponse, AIServiceType } from '../components/ThreeViewerContainer/utils/aiService';
import { ModelInfo } from '@/hooks/useModelDatabase';

export interface EnhancedAIResponse extends AIResponse {
  promptId: string;
  analysisId?: string;
}

export class EnhancedAIService {
  private aiService: AIService;
  private currentModelFileName: string | null = null;
  private currentModelId: string | null = null;
  private databaseEnabled: boolean = false;

  constructor(serviceType: AIServiceType = 'gemini') {
    this.aiService = new AIService(serviceType);
  }

  // Делегирование методов AIService
  registerObject(name: string, uuid: string): void {
    return this.aiService.registerObject(name, uuid);
  }

  clearObjects(): void {
    return this.aiService.clearObjects();
  }

  getRegisteredObjects(): any[] {
    return this.aiService.getRegisteredObjects();
  }

  async analyzeScene(
    scene: THREE.Scene,
    useFallback: boolean = false,
  ): Promise<void> {
    return this.aiService.analyzeScene(scene, useFallback);
  }

  async processPrompt(prompt: string, scene: THREE.Scene): Promise<AIResponse> {
    return this.aiService.processPrompt(prompt, scene);
  }

  applyChanges(scene: THREE.Scene, changes: any[]): void {
    return this.aiService.applyChanges(scene, changes);
  }

  get serviceType(): AIServiceType {
    return this.aiService.getServiceType();
  }

  // Включить/выключить работу с базой данных
  enableDatabase(): void {
    this.databaseEnabled = true;
  }

  disableDatabase(): void {
    this.databaseEnabled = false;
  }

  // Проверить, включена ли работа с базой данных
  isDatabaseEnabled(): boolean {
    return this.databaseEnabled;
  }

  // Установить текущую модель для работы с базой данных
  setCurrentModel(model: ModelInfo): void {
    this.currentModelFileName = model.fileName;
    this.currentModelId = model.id;
    this.enableDatabase();
  }

  // Установить текущую модель без работы с базой данных
  setCurrentModelWithoutDatabase(fileName: string): void {
    this.currentModelFileName = fileName;
    this.currentModelId = null;
    this.disableDatabase();
  }

  // Анализ сцены с сохранением в базу данных
  async analyzeSceneWithDatabase(
    scene: THREE.Scene,
    useFallback: boolean = false,
  ): Promise<string | null> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    // Выполнить анализ сцены
    await this.analyzeScene(scene, useFallback);

    // Получить результаты анализа
    const registeredObjects = this.getRegisteredObjects();

    // Подготовить данные анализа
    const analysisResult = {
      objectCount: registeredObjects.length,
      objects: registeredObjects.map(obj => ({
        uuid: obj.uuid,
        name: obj.name,
        category: obj.category || 'unknown',
        confidence: obj.confidence || 0,
      })),
      analysisMethod: useFallback ? 'basic' : 'ai',
      timestamp: new Date().toISOString(),
    };

    // Сохранить анализ в базу данных
    const analysis = await ModelService.saveAnalysis(this.currentModelId, {
      analysisType: 'scene_analysis',
      result: analysisResult,
      metadata: {
        useFallback,
        serviceType: this.serviceType,
      },
    });

    console.log(`Scene analysis saved with ID: ${analysis.id}`);
    return analysis.id;
  }

  // Обработка промпта с сохранением в базу данных
  async processPromptWithDatabase(
    prompt: string,
    scene: THREE.Scene,
    promptType: string = 'modification',
  ): Promise<EnhancedAIResponse> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    // Сохранить промпт в базу данных
    const savedPrompt = await ModelService.savePrompt(this.currentModelId, {
      userPrompt: prompt,
      promptType,
      metadata: {
        serviceType: this.serviceType,
        timestamp: new Date().toISOString(),
      },
    });

    try {
      // Обработать промпт с помощью AI
      const aiResponse = await this.processPrompt(prompt, scene);

      // Обновить промпт с ответом AI
      await ModelService.updatePromptWithResponse(
        savedPrompt.id,
        JSON.stringify(aiResponse),
        'completed',
      );

      // Сохранить состояние модели после изменений
      await this.saveCurrentModelState(scene);

      return {
        ...aiResponse,
        promptId: savedPrompt.id,
      };
    } catch (error) {
      // Обновить промпт со статусом ошибки
      await ModelService.updatePromptWithResponse(
        savedPrompt.id,
        error instanceof Error ? error.message : 'Unknown error',
        'failed',
      );

      throw error;
    }
  }

  // Универсальный метод анализа сцены (с БД или без)
  async analyzeSceneUniversal(
    scene: THREE.Scene,
    useFallback: boolean = false,
  ): Promise<string | null> {
    if (this.databaseEnabled && this.currentModelId) {
      return await this.analyzeSceneWithDatabase(scene, useFallback);
    } else {
      await this.analyzeScene(scene, useFallback);
      return null;
    }
  }

  // Универсальный метод обработки промпта (с БД или без)
  async processPromptUniversal(
    prompt: string,
    scene: THREE.Scene,
    promptType: string = 'modification',
  ): Promise<AIResponse | EnhancedAIResponse> {
    if (this.databaseEnabled && this.currentModelId) {
      return await this.processPromptWithDatabase(prompt, scene, promptType);
    } else {
      return await this.processPrompt(prompt, scene);
    }
  }

  // Сохранение текущего состояния модели
  async saveCurrentModelState(scene: THREE.Scene): Promise<void> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    const objectStates: any[] = [];

    scene.traverse(object => {
      if (object instanceof THREE.Mesh && object.uuid) {
        objectStates.push({
          objectId: object.uuid,
          objectName: object.name || undefined,
          position: {
            x: object.position.x,
            y: object.position.y,
            z: object.position.z,
          },
          rotation: {
            x: object.rotation.x,
            y: object.rotation.y,
            z: object.rotation.z,
          },
          scale: {
            x: object.scale.x,
            y: object.scale.y,
            z: object.scale.z,
          },
          visible: object.visible,
          selected: false, // Можно добавить логику для определения выбранных объектов
          properties: {
            materialType: object.material?.type,
            geometryType: object.geometry?.type,
            // Добавить другие свойства по необходимости
          },
        });
      }
    });

    await ModelService.saveModelState(this.currentModelId, objectStates);
    console.log(`Saved state for ${objectStates.length} objects`);
  }

  // Загрузка состояния модели
  async loadModelState(scene: THREE.Scene): Promise<void> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    const objectStates = await ModelService.getModelState(this.currentModelId);

    if (objectStates.length === 0) {
      console.log('No saved state found for current model');
      return;
    }

    // Создать карту объектов сцены по UUID
    const sceneObjects = new Map<string, THREE.Mesh>();
    scene.traverse(object => {
      if (object instanceof THREE.Mesh && object.uuid) {
        sceneObjects.set(object.uuid, object);
      }
    });

    // Применить сохраненное состояние к объектам
    let appliedCount = 0;
    for (const state of objectStates) {
      const object = sceneObjects.get(state.objectId);
      if (object) {
        object.position.set(
          state.position.x,
          state.position.y,
          state.position.z,
        );
        object.rotation.set(
          state.rotation.x,
          state.rotation.y,
          state.rotation.z,
        );
        object.scale.set(state.scale.x, state.scale.y, state.scale.z);
        object.visible = state.visible ?? true;

        if (state.objectName) {
          object.name = state.objectName;
        }

        appliedCount++;
      }
    }

    console.log(`Applied saved state to ${appliedCount} objects`);
  }

  // Получение истории промптов для текущей модели
  async getPromptHistory(limit: number = 10): Promise<any[]> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    return await ModelService.getPromptHistory(this.currentModelId, limit);
  }

  // Получение последнего анализа для текущей модели
  async getModelAnalysis(): Promise<any | null> {
    if (!this.currentModelId) {
      throw new Error('No current model set. Call setCurrentModel first.');
    }

    const analysis = await ModelService.getModelAnalysis(this.currentModelId);

    if (!analysis) {
      return null;
    }

    return {
      ...analysis,
      result: JSON.parse(analysis.result),
      metadata: analysis.metadata ? JSON.parse(analysis.metadata) : null,
    };
  }

  // Получение информации о текущей модели
  async getCurrentModelInfo(): Promise<any | null> {
    if (!this.currentModelFileName) {
      return null;
    }

    return await ModelService.getModelByFileName(this.currentModelFileName);
  }

  // Очистка текущей модели
  clearCurrentModel(): void {
    this.currentModelFileName = null;
    this.currentModelId = null;
    console.log('Cleared current model');
  }
}
