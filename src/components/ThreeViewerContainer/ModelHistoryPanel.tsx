'use client';

import { useState, useEffect } from 'react';
import { useModelDatabase, type PromptInfo, type AnalysisInfo, type ModelInfo } from '@/hooks/useModelDatabase';

interface ModelHistoryPanelProps {
  fileName: string | undefined;
  isVisible: boolean;
  onClose: () => void;
}

export default function ModelHistoryPanel({
  fileName,
  isVisible,
  onClose,
}: ModelHistoryPanelProps) {
  const [activeTab, setActiveTab] = useState<'prompts' | 'analysis' | 'info'>(
    'prompts',
  );
  const [prompts, setPrompts] = useState<PromptInfo[]>([]);
  const [analysis, setAnalysis] = useState<AnalysisInfo | null>(null);
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const { loading, error, getPromptHistory, getAnalysis, getModelByFileName } =
    useModelDatabase();

  // Загрузка данных при открытии панели или смене файла
  useEffect(() => {
    if (isVisible && fileName) {
      loadData();
    }
  }, [isVisible, fileName]);

  const loadData = async () => {
    if (!fileName) return;

    setRefreshing(true);
    try {
      // Загрузить информацию о модели
      const model = await getModelByFileName(fileName);
      setModelInfo(model);

      // Загрузить историю промптов
      const promptHistory = await getPromptHistory(fileName, 20);
      setPrompts(promptHistory);

      // Загрузить анализ
      const analysisData = await getAnalysis(fileName, 'scene_analysis');
      setAnalysis(analysisData);
    } catch (err) {
      console.error('Error loading model data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold text-gray-800">
            Model History: {fileName}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('info')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'info'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Model Info
          </button>
          <button
            onClick={() => setActiveTab('prompts')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'prompts'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Prompts ({prompts.length})
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'analysis'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Analysis ({analysis ? 1 : 0})
          </button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {loading || refreshing ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading...</span>
            </div>
          ) : error ? (
            <div className="text-red-600 text-center py-8">Error: {error}</div>
          ) : (
            <>
              {/* Model Info Tab */}
              {activeTab === 'info' && (
                <div className="space-y-4">
                  {modelInfo ? (
                    <>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            File Name
                          </label>
                          <p className="text-gray-900">{modelInfo.fileName}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            File Size
                          </label>
                          <p className="text-gray-900">
                            {modelInfo.fileSize
                              ? `${(modelInfo.fileSize / 1024 / 1024).toFixed(2)} MB`
                              : 'Unknown'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Uploaded
                          </label>
                          <p className="text-gray-900">
                            {formatDate(modelInfo.uploadedAt)}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Last Updated
                          </label>
                          <p className="text-gray-900">
                            {formatDate(modelInfo.updatedAt)}
                          </p>
                        </div>
                      </div>

                      {modelInfo._count && (
                        <div className="mt-6">
                          <h3 className="text-lg font-medium text-gray-800 mb-2">
                            Statistics
                          </h3>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center p-3 bg-gray-50 rounded">
                              <div className="text-2xl font-bold text-blue-600">
                                {modelInfo.analysis ? 1 : 0}
                              </div>
                              <div className="text-sm text-gray-600">
                                Analysis
                              </div>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded">
                              <div className="text-2xl font-bold text-green-600">
                                {modelInfo._count.prompts}
                              </div>
                              <div className="text-sm text-gray-600">
                                Prompts
                              </div>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded">
                              <div className="text-2xl font-bold text-purple-600">
                                {modelInfo._count.modelStates}
                              </div>
                              <div className="text-sm text-gray-600">
                                Saved States
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-600 text-center py-8">
                      No model information available
                    </p>
                  )}
                </div>
              )}

              {/* Prompts Tab */}
              {activeTab === 'prompts' && (
                <div className="space-y-4">
                  {prompts.length > 0 ? (
                    prompts.map(prompt => (
                      <div
                        key={prompt.id}
                        className="border rounded-lg p-4 bg-gray-50"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">
                            {prompt.promptType}
                          </span>
                          <div className="flex items-center space-x-2">
                            <span
                              className={`text-sm font-medium ${getStatusColor(prompt.status)}`}
                            >
                              {prompt.status}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDate(prompt.createdAt)}
                            </span>
                          </div>
                        </div>

                        <div className="mb-2">
                          <p className="text-sm font-medium text-gray-700">
                            User Prompt:
                          </p>
                          <p className="text-gray-900 bg-white p-2 rounded border">
                            {prompt.userPrompt}
                          </p>
                        </div>

                        {prompt.aiResponse && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">
                              AI Response:
                            </p>
                            <div className="text-gray-900 bg-white p-2 rounded border max-h-32 overflow-y-auto">
                              <pre className="whitespace-pre-wrap text-sm">
                                {typeof prompt.aiResponse === 'string'
                                  ? prompt.aiResponse
                                  : JSON.stringify(prompt.aiResponse, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-600 text-center py-8">
                      No prompts found
                    </p>
                  )}
                </div>
              )}

              {/* Analysis Tab */}
              {activeTab === 'analysis' && (
                <div className="space-y-4">
                  {analysis ? (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          {analysis.analysisType}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDate(analysis.createdAt)}
                        </span>
                      </div>

                      <div className="bg-white p-3 rounded border">
                        <pre className="whitespace-pre-wrap text-sm text-gray-900 max-h-64 overflow-y-auto">
                          {JSON.stringify(analysis.result, null, 2)}
                        </pre>
                      </div>

                      {analysis.metadata && (
                        <div className="mt-2">
                          <p className="text-sm font-medium text-gray-700">
                            Metadata:
                          </p>
                          <div className="bg-white p-2 rounded border">
                            <pre className="whitespace-pre-wrap text-xs text-gray-600">
                              {JSON.stringify(analysis.metadata, null, 2)}
                            </pre>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-600 text-center py-8">
                      No analysis found
                    </p>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t bg-gray-50">
          <button
            onClick={loadData}
            disabled={loading || refreshing}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            Refresh
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
